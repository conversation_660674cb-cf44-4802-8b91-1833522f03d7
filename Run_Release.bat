@echo off
chcp 65001 >nul
echo ========================================
echo      نظام إدارة الزيارات الميدانية
echo      النسخة المحسنة - Release
echo ========================================
echo.

cd /d "%~dp0"

REM Check if Release build exists
if exist "bin\Release\net9.0-windows\SFDSystem.exe" (
    echo ✅ تشغيل النسخة المحسنة...
    echo 🚀 بدء التطبيق...
    echo.
    "bin\Release\net9.0-windows\SFDSystem.exe"
) else (
    echo ⚠️ النسخة المحسنة غير موجودة
    echo 🔧 بناء النسخة المحسنة...
    echo.
    
    dotnet clean
    dotnet build --configuration Release
    
    if exist "bin\Release\net9.0-windows\SFDSystem.exe" (
        echo ✅ تم بناء النسخة المحسنة بنجاح
        echo 🚀 بدء التطبيق...
        echo.
        "bin\Release\net9.0-windows\SFDSystem.exe"
    ) else (
        echo ❌ فشل في بناء النسخة المحسنة
        echo 🔄 محاولة التشغيل العادي...
        dotnet run --configuration Release
    )
)

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ حدث خطأ في تشغيل التطبيق
    echo 💡 تحقق من:
    echo    - وجود .NET 9.0 Runtime
    echo    - صحة ملفات المشروع
    echo    - إعدادات قاعدة البيانات
    echo.
    pause
)

echo.
echo ========================================
echo تم إغلاق التطبيق
echo ========================================
pause
