using System;
using System.Threading.Tasks;
using System.Windows;
using DriverManagementSystem.Models;
using DriverManagementSystem.Services;

namespace DriverManagementSystem
{
    /// <summary>
    /// اختبار منع تكرار رقم الزيارة
    /// </summary>
    public static class TestDuplicateVisitNumber
    {
        public static async Task RunTestAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🧪 بدء اختبار منع تكرار رقم الزيارة");
                
                var dataService = new DataService();
                
                // إنشاء زيارة تجريبية أولى
                var firstVisit = new FieldVisit
                {
                    VisitNumber = "TEST-001",
                    AddDate = DateTime.Now,
                    HijriDate = "1446/01/01",
                    DepartureDate = DateTime.Now.AddDays(1),
                    ReturnDate = DateTime.Now.AddDays(3),
                    DaysCount = 2,
                    MissionPurpose = "اختبار الزيارة الأولى",
                    SectorName = "قطاع تجريبي",
                    VisitorsCount = 1
                };

                System.Diagnostics.Debug.WriteLine("📝 إضافة الزيارة الأولى...");
                var (firstSuccess, firstErrors) = await dataService.AddFieldVisitAsync(firstVisit);
                
                if (firstSuccess)
                {
                    System.Diagnostics.Debug.WriteLine("✅ تم حفظ الزيارة الأولى بنجاح");
                    
                    // محاولة إضافة زيارة ثانية بنفس الرقم
                    var secondVisit = new FieldVisit
                    {
                        VisitNumber = "TEST-001", // نفس الرقم
                        AddDate = DateTime.Now,
                        HijriDate = "1446/01/02",
                        DepartureDate = DateTime.Now.AddDays(2),
                        ReturnDate = DateTime.Now.AddDays(4),
                        DaysCount = 2,
                        MissionPurpose = "اختبار الزيارة الثانية - يجب أن تفشل",
                        SectorName = "قطاع تجريبي آخر",
                        VisitorsCount = 2
                    };

                    System.Diagnostics.Debug.WriteLine("📝 محاولة إضافة زيارة ثانية بنفس الرقم...");
                    var (secondSuccess, secondErrors) = await dataService.AddFieldVisitAsync(secondVisit);
                    
                    if (!secondSuccess && secondErrors.Count > 0)
                    {
                        System.Diagnostics.Debug.WriteLine("✅ النظام منع التكرار بنجاح!");
                        System.Diagnostics.Debug.WriteLine($"📋 رسالة الخطأ: {secondErrors[0]}");
                        
                        MessageBox.Show(
                            "✅ اختبار منع تكرار رقم الزيارة نجح!\n\n" +
                            "النظام منع حفظ الزيارة الثانية بنفس الرقم وعرض رسالة خطأ مناسبة.\n\n" +
                            $"رسالة الخطأ: {secondErrors[0]}",
                            "نجح الاختبار",
                            MessageBoxButton.OK,
                            MessageBoxImage.Information
                        );
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("❌ النظام لم يمنع التكرار!");
                        MessageBox.Show(
                            "❌ فشل الاختبار!\n\n" +
                            "النظام لم يمنع حفظ الزيارة الثانية بنفس الرقم.",
                            "فشل الاختبار",
                            MessageBoxButton.OK,
                            MessageBoxImage.Error
                        );
                    }
                    
                    // تنظيف البيانات التجريبية
                    System.Diagnostics.Debug.WriteLine("🧹 تنظيف البيانات التجريبية...");
                    var visits = await dataService.GetFieldVisitsAsync();
                    foreach (var visit in visits)
                    {
                        if (visit.VisitNumber == "TEST-001")
                        {
                            await dataService.DeleteFieldVisitAsync(visit.Id);
                            System.Diagnostics.Debug.WriteLine($"🗑️ تم حذف الزيارة: {visit.Id}");
                        }
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("❌ فشل في حفظ الزيارة الأولى");
                    var errorMessage = "فشل في حفظ الزيارة الأولى:\n";
                    foreach (var error in firstErrors)
                    {
                        errorMessage += $"• {error}\n";
                    }
                    MessageBox.Show(errorMessage, "خطأ في الاختبار", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في الاختبار: {ex.Message}");
                MessageBox.Show($"خطأ في اختبار منع تكرار رقم الزيارة:\n{ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
